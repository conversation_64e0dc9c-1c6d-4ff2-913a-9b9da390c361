<!doctype html>
<html class="no-js" lang="TR">

<head>
    <title>Anasayfa | 16. <PERSON><PERSON><PERSON>troenteroloji, <PERSON><PERSON><PERSON><PERSON><PERSON> ve Beslenme Kongresi</title>
    <?php include('head.php') ?>
</head>

<body>

    <?php include('header.php') ?>

    <!--======== Hero Section ========-->
    <section class="vs-hero-wrapper-four position-relative" data-bg-src="/doc/slider-background.jpg">
        <div class="container">
            <div class="hero-content3">
                <img class="slider-logo" src="/doc/logo-ikili.png">
                <img class="slider-metin" src="/doc/slider-metin.png">
            </div>
        </div>
        <img class="layer hero-img-2" data-depth="0.2" src="doc/slider-foto.png" alt="shape">
        <!-- <div class="shape-mockup movingTopLeft d-none d-md-block" data-bottom="43%" data-right="10%"><img src="doc/kus.png" alt="shapes"></div> -->
    </section>
    <!--======== Hero Section ========-->



    <section class="service-section space">
        <div class="container">

            <div class="row gy-20">
                <!-- Single item -->
                <div class="col-lg-3 col-md-6 col-6 service-card wow fadeInUp" data-wow-delay="0.1s" onclick="location.href='kurullar.php';" style="cursor: pointer;">
                    <div class="service-card-inner">
                        <h2 class="sr-title h4"><a href="/kurullar.php">Kurullar</a></h2>
                    </div>
                </div>
                <!-- Single item -->
                <div class="col-lg-3 col-md-6 col-6 service-card wow fadeInUp" data-wow-delay="0.1s" onclick="location.href='genel-bilgiler.php';" style="cursor: pointer;">
                    <div class="service-card-inner">
                        <h2 class="sr-title h4"><a href="/genel-bilgiler.php">Genel Bilgiler</a></h2>
                    </div>
                </div>
                <!-- Single item -->
                <div class="col-lg-3 col-md-6 col-6 service-card wow fadeInUp" data-wow-delay="0.1s" onclick="location.href='kayit-konaklama.php';" style="cursor: pointer;">
                    <div class="service-card-inner">
                        <h2 class="sr-title h4"><a href="/kayit-konaklama.php">Kayıt & Konaklama</a></h2>
                    </div>
                </div>
                <!-- Single item -->
                <div class="col-lg-3 col-md-6 col-6 service-card wow fadeInUp" data-wow-delay="0.1s" onclick="location.href='iletisim.php';" style="cursor: pointer;">
                    <div class="service-card-inner">
                        <h2 class="sr-title h4"><a href="/iletisim.php">İletişim</a></h2>
                    </div>
                </div>
            </div>
        </div>

    </section>

    <!--======== Counter Section ========-->
    <section class="counter-section space-double" data-overlay="title" data-opacity="8">
        <div class="container">
            <!-- <div class="row text-center justify-content-center wow fadeInUp" style="visibility: visible; animation-delay: 0.1s; animation-name: fadeInUp;">
                <div class="col-xl-6 col-lg-7 col-md-8 col-sm-9">
                    <div class="title-area">
                        <h2 class="sec-title" style="color: #fff">Kongreye Kalan Süre</h2>
                    </div>
                </div>
            </div> -->
            <div class="row justify-content-center">
                <div class="col-md-6 col-12">
                    <div class="row gy-30 justify-content-between">
                        <div class="col-xl-auto col-sm-3 col-3 wow fadeInUp" data-wow-delay="0.1s">
                            <div class="counter-box">
                                <div class="counter-info text-center">
                                    <h2 id="days" class="counter-number"></h2>
                                </div>
                            </div>
                        </div>
                        <div class="col-xl-auto col-sm-3 col-3 wow fadeInUp" data-wow-delay="0.1s">
                            <div class="counter-box">
                                <div class="counter-info text-center">
                                    <h2 id="hours" class="counter-number"></h2>
                                </div>
                            </div>
                        </div>
                        <div class="col-xl-auto col-sm-3 col-3 wow fadeInUp" data-wow-delay="0.1s">
                            <div class="counter-box">
                                <div class="counter-info text-center">
                                    <h2 id="minutes" class="counter-number"></h2>
                                </div>
                            </div>
                        </div>
                        <div class="col-xl-auto col-sm-3 col-3 wow fadeInUp" data-wow-delay="0.1s">
                            <div class="counter-box">
                                <div class="counter-info text-center">
                                    <h2 id="seconds" class="counter-number"></h2>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="section-before style-2"><img src="/assets/img/shape/counter-before-1.png" alt="shape"></div>
        <div class="section-after style-2"><img src="/assets/img/shape/counter-after-1.png" alt="shape"></div>

        <div class="shape-mockup z-index-3 d-none d-hd-block" data-bottom="-9%" data-right="7%"><img src="/assets/img/shape/line-3.png" alt="shapes"></div>
    </section>


    <!--======== / Counter Section ========-->

    <!--======== About Section ========-->
    <section class="about-section pt-40 pb-30 mb-80">
        <div class="container">
            <div class="row gy-30 align-items-center justify-content-center">
                <div class="col-lg-8 wow fadeInLeft" data-wow-delay="0.1s">
                    <span class="sub-title">16. Ulusal Çocuk Gastroenteroloji, Hepatoloji ve Beslenme Kongresi</span>
                    <h2 class="sec-title big-title">Davet</h2>
                    <?php include('davet-metni.php') ?>
                </div>
            </div>

        </div>
    </section>
    <!--======== / About Section ========-->

    <?php include('footer.php') ?>
    
    <script>
        // Set the date we're counting down to
        var countDownDate = new Date("Oct 9, 2025 10:00:00").getTime();

        // Update the count down every 1 second
        var x = setInterval(function() {

            // Get today's date and time
            var now = new Date().getTime();

            // Find the distance between now and the count down date
            var distance = countDownDate - now;

            // Time calculations for days, hours, minutes and seconds
            var days = Math.floor(distance / (1000 * 60 * 60 * 24));
            var hours = Math.floor((distance % (1000 * 60 * 60 * 24)) / (1000 * 60 * 60));
            var minutes = Math.floor((distance % (1000 * 60 * 60)) / (1000 * 60));
            var seconds = Math.floor((distance % (1000 * 60)) / 1000);

            // Display the result in the element with id="demo"
            $("#days").html(days + "<br><span>Gün</span>");
            $("#hours").html(hours + "<br><span>Saat</span>");
            $("#minutes").html(minutes + "<br><span>Dakika</span>");
            $("#seconds").html(seconds + "<br><span>Saniye</span>");

            // If the count down is finished, write some text
            if (distance < 0) {
                clearInterval(x);
                document.getElementById("demo").innerHTML = "EXPIRED";
            }
        }, 1000);
    </script>

<?php include('script.php') ?>

</body>

</html>